import 'dart:convert';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:intl/intl.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/notifications/notification_service.dart';
import 'package:timezone/timezone.dart' as tz;

class NotificationScheduler {
  final FlutterLocalNotificationsPlugin _plugin;
  NotificationScheduler(this._plugin);

  Future<void> scheduleFor(PanchangDataForThreeDays data) async {
    final now = DateTime.now();
    final loc = NotificationService.instance.tzLoc;

    final items = <({String nameKey, String display, DateTime start, DateTime end})>[];

    void add(String nameKey, String display, PanchangSomeTimeItem? item) {
      if (item == null) return;
      if (item.start.isAfter(now)) {
        items.add((nameKey: nameKey, display: display, start: item.start, end: item.end));
      }
    }


    // Selected single-slot muhurtas
    add('bramha<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', data.currentDay.bramhaMuhrat);
    add('abhijit', 'Abhijit', data.currentDay.abhijit);
    add('godhuli', 'Godhuli', data.currentDay.godhuli);
    add('pratahSandhya', 'Pratah Sandhya', data.currentDay.pratahSandhya);
    add('vijayMuhurat', 'Vijay Muhurat', data.currentDay.vijayMuhurat);
    add('sayahnaSandhya', 'Sayahna Sandhya', data.currentDay.sayahnaSandhya);
    add('nishitaMuhurta', 'Nishita Muhurta', data.currentDay.nishitaMuhurta);
    add('rahuKal', 'Rahu Kaal', data.currentDay.rahuKal);
    add('gulikaiKal', 'Gulikai Kaal', data.currentDay.gulikaiKal);
    add('yamaganda', 'Yamaganda', data.currentDay.yamaganda);

    add('bramhaMuhrat', 'Bramha Muhurat', data.nextDay.bramhaMuhrat);
    add('abhijit', 'Abhijit', data.nextDay.abhijit);
    add('godhuli', 'Godhuli', data.nextDay.godhuli);
    add('pratahSandhya', 'Pratah Sandhya', data.nextDay.pratahSandhya);
    add('vijayMuhurat', 'Vijay Muhurat', data.nextDay.vijayMuhurat);
    add('sayahnaSandhya', 'Sayahna Sandhya', data.nextDay.sayahnaSandhya);
    add('nishitaMuhurta', 'Nishita Muhurta', data.nextDay.nishitaMuhurta);
    add('rahuKal', 'Rahu Kaal', data.nextDay.rahuKal);
    add('gulikaiKal', 'Gulikai Kaal', data.nextDay.gulikaiKal);
    add('yamaganda', 'Yamaganda', data.nextDay.yamaganda);

    // Multi-slot lists
    void addList(String key, String display, List<PanchangSomeTimeItem> list) {
      for (final item in list) {
        if (item.start.isAfter(now)) {
          items.add((nameKey: key, display: display, start: item.start, end: item.end));
        }
      }
    }

    addList('durMuhurtam', 'Dur Muhurtam', data.currentDay.durMuhurtam);
    addList('varjyam', 'Varjyam', data.currentDay.varjyam);
    addList('amritKal', 'Amrit Kal', data.currentDay.amritKal);

    addList('durMuhurtam', 'Dur Muhurtam', data.nextDay.durMuhurtam);
    addList('varjyam', 'Varjyam', data.nextDay.varjyam);
    addList('amritKal', 'Amrit Kal', data.nextDay.amritKal);

    // Schedule
    const details = NotificationDetails(
      android: AndroidNotificationDetails(
        'muhurta_channel',
        'Muhurtas',
        channelDescription: 'Muhurta alerts',
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'ticker',
      ),
    );

    print('NotificationScheduler: scheduling ${items.length} notifications');

    for (final s in items) {
      try {
        final id = _stableId(s.nameKey, s.start);
        final startTz = tz.TZDateTime.from(s.start, loc);

        await _plugin.zonedSchedule(
          id,
          s.display,
          'Starts now (${_fmt(s.start)}–${_fmt(s.end)})',
          startTz,
          details,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          payload: jsonEncode({'nameKey': s.nameKey, 'start': s.start.toIso8601String()}),
        );
      } catch (e) {
        print('Error scheduling notification: $e');
        // ignore individual failures, continue scheduling
      }
    }
  }

  int _stableId(String nameKey, DateTime start) {
    final key = '${start.year}${start.month.toString().padLeft(2, '0')}${start.day.toString().padLeft(2, '0')}|$nameKey|${start.hour}${start.minute}';
    return key.hashCode & 0x7fffffff;
  }

  String _fmt(DateTime dt) => DateFormat('HH:mm').format(dt);
}


