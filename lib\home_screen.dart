import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:panchang_at_this_moment/components/map_selection.dart';
import 'package:panchang_at_this_moment/components/time_control_widget.dart';
import 'package:panchang_at_this_moment/main.dart';
import 'package:panchang_at_this_moment/panchang_at_this_moment.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:panchang_at_this_moment/stores/panchang_store.dart';
import 'package:panchang_at_this_moment/notifications/notification_service.dart';
import 'package:panchang_at_this_moment/notifications/notification_scheduler.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  DateTime _startTime =
      DateTime.now().add(const Duration(days: 0, hours: 0, minutes: 0));

  // Time control state
  DateTime _currentTime = DateTime.now();
  bool _isPlaying = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _currentTime = _startTime;
    // Store initialization is handled in main.dart
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Reset timer to current time when app comes back to foreground
    if (state == AppLifecycleState.resumed) {
      _resetToNow();
    }
  }

  void _onTimeChanged(DateTime newTime) {
    setState(() {
      _currentTime = newTime;
    });
  }

  void _onPlayStateChanged(bool isPlaying) {
    setState(() {
      _isPlaying = isPlaying;
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });
  }

  void _resetToNow() async {
    final store = Provider.of<PanchangStore>(context, listen: false);
    setState(() {
      _currentTime = DateTime.now();
      _startTime = _currentTime;
    });
    await store.updateDate(DateTime.now());
  }

  Future<void> _getCurrentLocation() async {
    final store = Provider.of<PanchangStore>(context, listen: false);
    _showLocationMessage('Getting your location...');

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showLocationMessage('Location services are disabled. Please enable GPS and try again.');
        return;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showLocationMessage('Location permission denied. Using current location.');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showLocationMessage('Location permission permanently denied. Please enable it in settings.');
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      // Update location in store
      await store.updateLocation(position.latitude, position.longitude);

      _showLocationMessage('Location updated successfully! (${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)})');

    } catch (e) {
      debugPrint('Location error: $e');
      _showLocationMessage('Could not get current location: ${e.toString().split('.').last}. Using saved location.');
    }
  }

  void _showLocationMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: const Color(0xFF6B4E3D),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _selectLocation() {
    final store = Provider.of<PanchangStore>(context, listen: false);
    if (store.latitude == null || store.longitude == null) return;
    Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => MapSelectionScreen(
                onLocationSelected: (location) async {
                  final navigator = Navigator.of(context);
                  await store.updateLocation(location[0].toDouble(), location[1].toDouble());
                  navigator.pop();
                },
                initialLat: store.latitude!,
                initialLng: store.longitude!,
              )),
    );
  }

  void _selectTime(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startTime,
      firstDate: DateTime(1600),
      lastDate: DateTime(2400),
    );

    if (picked != null) {
      final TimeOfDay? timeOfDay = await showTimePicker(
        // ignore: use_build_context_synchronously
        context: context,
        initialTime: TimeOfDay.fromDateTime(_startTime),
      );

      if (timeOfDay != null) {
        final newDateTime = DateTime(
          picked.year,
          picked.month,
          picked.day,
          timeOfDay.hour,
          timeOfDay.minute,
        );
        _setNewTime(newDateTime);
      }
    }
  }

  void _setNewTime(DateTime newTime) async {
    final store = Provider.of<PanchangStore>(context, listen: false);
    setState(() {
      _startTime = newTime;
      _currentTime = newTime;
    });
    await store.updateDate(newTime);
  }

  String? _lastScheduledKey;

  Future<void> _maybeScheduleNotifications(PanchangStore store) async {
    if (!store.hasValidData || store.translatedPanchangData == null) return;

    final key = '${store.currentDate.toIso8601String()}|${store.latitude}|${store.longitude}|${Localizations.localeOf(context).languageCode}';
    if (_lastScheduledKey == key) return;

    _lastScheduledKey = key;
    debugPrint('HomeScreen: Scheduling notifications for key: $key');

    // Cancel existing and reschedule to avoid duplicates
    await NotificationService.instance.cancelAll();
    await NotificationScheduler(NotificationService.instance.plugin)
        .scheduleFor(store.translatedPanchangData!);
  }

  // Test function to schedule a notification in 10 seconds
  Future<void> _scheduleTestNotification() async {
    final now = DateTime.now();
    final testTime = now.add(const Duration(seconds: 10));

    debugPrint('Scheduling test notification for: $testTime');

    await NotificationService.instance.plugin.zonedSchedule(
      999,
      'Test Notification',
      'This is a test notification scheduled for 10 seconds from now',
      tz.TZDateTime.from(testTime, NotificationService.instance.tzLoc),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'muhurta_channel',
          'Muhurtas',
          channelDescription: 'Muhurta alerts',
          importance: Importance.max,
          priority: Priority.high,
        ),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Test notification scheduled for ${DateFormat('HH:mm:ss').format(testTime)}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            // ClipRRect(
            //   borderRadius: BorderRadius.circular(10),
            //   child: Image.asset(
            //     'assets/panchang_logo.jpg',
            //     width: 36,
            //     height: 36,
            //   ),
            // ),
            // const SizedBox(width: 12),
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.appTitle,
                style: const TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: TimeControlWidget(
            currentTime: _currentTime,
            isPlaying: _isPlaying,
            onPlayPause: _togglePlayPause,
            onReset: _resetToNow,
            onTimeChanged: _setNewTime,
          ),
        ),
        actions: [
          Builder(
            builder: (context) {
              return PopupMenuButton<String>(
                icon: const Icon(Icons.settings),
                onSelected: (value) {
                  switch (value) {
                    case 'selectTime':
                      _selectTime(context);
                      break;
                    case 'selectLocation':
                      _selectLocation();
                      break;
                    case 'getCurrentLocation':
                      _getCurrentLocation();
                      break;
                    case 'testNotification':
                      _scheduleTestNotification();
                      break;
                    case 'selectLanguage':
                      FocusScope.of(context).unfocus();
                      showDialog<void>(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: const Text('Select Language'),
                            content: SizedBox(
                              width: double.maxFinite,
                              height: 300,
                              child: GridView.builder(
                                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  childAspectRatio: 3,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                ),
                                itemCount: {
                                  'en': 'English',
                                  'hi': 'हिन्दी',
                                  'te': 'తెలుగు',
                                  'ta': 'தமிழ்',
                                  'kn': 'ಕನ್ನಡ',
                                  'ml': 'മലയാളം',
                                  'be': 'বাংলা',
                                  'mr': 'मराठी',
                                }.length,
                                itemBuilder: (context, index) {
                                  final languages = {
                                    'en': 'English',
                                    'hi': 'हिन्दी',
                                    'te': 'తెలుగు',
                                    'ta': 'தமிழ்',
                                    'kn': 'ಕನ್ನಡ',
                                    'ml': 'മലയാളം',
                                    'be': 'বাংলা',
                                    'mr': 'मराठी',
                                  };
                                  final entry = languages.entries.elementAt(index);
                                  final currentLocale = Localizations.localeOf(context).languageCode;
                                  final isSelected = currentLocale == entry.key;

                                  return GestureDetector(
                                    onTap: () {
                                      final store = Provider.of<PanchangStore>(context, listen: false);
                                      MyApp.of(context)?.setLocale(
                                          Locale.fromSubtags(
                                              languageCode: entry.key));
                                      store.updateLanguage(entry.key);
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: isSelected ? const Color(0xFF6B4E3D) : Colors.white,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: isSelected ? const Color(0xFF6B4E3D) : const Color(0xFFE0E0E0),
                                          width: 2,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.08),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: Text(
                                          entry.value,
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color: isSelected ? Colors.white : const Color(0xFF3E2723),
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      );
                      break;
                  }
                },
                itemBuilder: (context) {
                  return <PopupMenuItem<String>>[
                    const PopupMenuItem<String>(
                      value: 'selectTime',
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time_rounded,
                            color: Colors.orange,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Change Time',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'selectLocation',
                      child: Row(
                        children: [
                          Icon(
                            Icons.location_on_rounded,
                            color: Colors.green,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Change Location',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'getCurrentLocation',
                      child: Row(
                        children: [
                          Icon(
                            Icons.my_location_rounded,
                            color: Colors.teal,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Use Current Location',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'testNotification',
                      child: Row(
                        children: [
                          Icon(
                            Icons.notifications_active,
                            color: Colors.orange,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Test Notification (10s)',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'selectLanguage',
                      child: Row(
                        children: [
                          Icon(
                            Icons.language_rounded,
                            color: Colors.blue,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Change Language',
                          ),
                        ],
                      ),
                    ),
                  ];
                },
              );
            },
          ),
        ],
      ),
      body: Observer(
        builder: (context) {
          final store = Provider.of<PanchangStore>(context);

          _maybeScheduleNotifications(store);

          return !store.hasLocationData
              ? const Center(child: CircularProgressIndicator())
              : PanchangAtTheMoment(
                  key: Key('${store.currentDate}, ${store.latitude}, ${store.longitude}'),
                  startTime: _currentTime,
                  isPlaying: _isPlaying,
                  lat: store.latitude!,
                  lng: store.longitude!,
                  alt: store.altitude,
                  onTimeChanged: _onTimeChanged,
                  onPlayStateChanged: _onPlayStateChanged,
                );
        },
      ),
    );
  }
}