import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/data/panchang_stats.dart';

class UpcomingMuhurta {
  final String nameKey;
  final String displayName;
  final DateTime start;
  final DateTime end;
  final bool isPositive;

  UpcomingMuhurta({
    required this.nameKey,
    required this.displayName,
    required this.start,
    required this.end,
    required this.isPositive,
  });
}

List<UpcomingMuhurta> getUpcomingMuhurtas({
  required PanchangDataForThreeDays panchangData,
  required DateTime currentTime,
  required BuildContext context,
}) {
  final List<UpcomingMuhurta> upcomingMuhurtas = [];
  final localizations = AppLocalizations.of(context)!;
  final panchangStats = getPanchangStats(context);

  // Muhurta config used for both current and upcoming
  final muhurtaConfigs = [
    (
      nameKey: 'bramhaMuhrat',
      display: localizations.bramhaMuhrat,
      get: (PanchangData d) => d.bramha<PERSON>uhrat,
    ),
    (
      nameKey: 'abhijit',
      display: localizations.abhijit,
      get: (PanchangData d) => d.abhijit,
    ),
    (
      nameKey: 'godhuli',
      display: localizations.godhuli,
      get: (PanchangData d) => d.godhuli,
    ),
    (
      nameKey: 'pratahSandhya',
      display: localizations.pratahSandhya,
      get: (PanchangData d) => d.pratahSandhya,
    ),
    (
      nameKey: 'vijayMuhurat',
      display: localizations.vijayMuhurat,
      get: (PanchangData d) => d.vijayMuhurat,
    ),
    (
      nameKey: 'sayahnaSandhya',
      display: localizations.sayahnaSandhya,
      get: (PanchangData d) => d.sayahnaSandhya,
    ),
    (
      nameKey: 'nishitaMuhurta',
      display: localizations.nishitaMuhurta,
      get: (PanchangData d) => d.nishitaMuhurta,
    ),
    (
      nameKey: 'rahuKal',
      display: localizations.rahuKal,
      get: (PanchangData d) => d.rahuKal,
    ),
    (
      nameKey: 'gulikaiKal',
      display: localizations.gulikaiKal,
      get: (PanchangData d) => d.gulikaiKal,
    ),
    (
      nameKey: 'yamaganda',
      display: localizations.yamaganda,
      get: (PanchangData d) => d.yamaganda,
    ),
  ];

  void addUpcomingMuhurta(String nameKey, String displayName, DateTime start, DateTime end) {
    final stats = panchangStats[nameKey] as Map;
    upcomingMuhurtas.add(UpcomingMuhurta(
      nameKey: nameKey,
      displayName: displayName,
      start: start,
      end: end,
      isPositive: stats['isPositive'],
    ));
  }

  for (final dayData in [panchangData.currentDay, panchangData.nextDay]) {
    for (final c in muhurtaConfigs) {
      final muhurta = c.get(dayData);
      if (muhurta != null && muhurta.start.isAfter(currentTime)) {
        addUpcomingMuhurta(c.nameKey, c.display, muhurta.start, muhurta.end);
      }
    }

    for (final item in dayData.durMuhurtam) {
      if (item.start.isAfter(currentTime)) {
        addUpcomingMuhurta('durMuhurtam', localizations.durMuhurtam, item.start, item.end);
      }
    }
    for (final item in dayData.varjyam) {
      if (item.start.isAfter(currentTime)) {
        addUpcomingMuhurta('varjyam', localizations.varjyam, item.start, item.end);
      }
    }
    for (final item in dayData.amritKal) {
      if (item.start.isAfter(currentTime)) {
        addUpcomingMuhurta('amritKal', localizations.amritKal, item.start, item.end);
      }
    }
  }

  upcomingMuhurtas.sort((a, b) => a.start.compareTo(b.start));
  return upcomingMuhurtas.take(3).toList();
}

Widget buildUpcomingMuhurtasWidget({
  required PanchangDataForThreeDays panchangData,
  required DateTime currentTime,
  required BuildContext context,
}) {
  final upcomingMuhurtas = getUpcomingMuhurtas(
    panchangData: panchangData,
    currentTime: currentTime,
    context: context,
  );
  if (upcomingMuhurtas.isEmpty) return const SizedBox.shrink();

  final loc = AppLocalizations.of(context)!;

  return Container(
    width: double.infinity,
    margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16.0),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.06),
          blurRadius: 6,
          spreadRadius: 0,
          offset: const Offset(0, 2),
        ),
      ],
      border: Border.all(color: const Color(0xFFEDE7E3), width: 0.6),
    ),
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.schedule,
                  color: Theme.of(context).colorScheme.primary,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  loc.upcomingMuhurtas,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontSize: 19,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                for (int i = 0; i < upcomingMuhurtas.length; i++) ...[
                  _buildMuhurtaItem(upcomingMuhurtas[i], context, currentTime),
                  if (i < upcomingMuhurtas.length - 1) const Divider(height: 8),
                ]
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildMuhurtaItem(UpcomingMuhurta muhurta, BuildContext context, DateTime currentTime) {
  final timeUntilStart = muhurta.start.difference(currentTime);
  final hoursUntil = timeUntilStart.inHours;
  final minutesUntil = timeUntilStart.inMinutes % 60;
  final timeUntilText = hoursUntil > 0 ? '${hoursUntil}h ${minutesUntil}m' : '${minutesUntil}m';

  final startTime = '${muhurta.start.hour.toString().padLeft(2, '0')}:${muhurta.start.minute.toString().padLeft(2, '0')}';
  final endTime = '${muhurta.end.hour.toString().padLeft(2, '0')}:${muhurta.end.minute.toString().padLeft(2, '0')}';

  return Container(
    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 10),
    decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: muhurta.isPositive ? const Color(0xFF4CAF50) : const Color(0xFFFF5722),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                muhurta.displayName,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                  color: const Color(0xFF3E2723),
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              const SizedBox(height: 1),
              Text(
                '$startTime - $endTime',
                style: Theme.of(context).textTheme.labelMedium!.copyWith(
                  color: const Color(0xFF8D6E63),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 1),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.06),
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.18),
              width: 0.7,
            ),
          ),
          child: Text(
            'in $timeUntilText',
            style: Theme.of(context).textTheme.labelSmall!.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    ),
  );
}

