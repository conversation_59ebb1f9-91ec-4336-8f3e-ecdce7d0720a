import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:panchang_at_this_moment/components/panchang_item_always_change.dart';
import 'package:panchang_at_this_moment/components/panchang_item_sometime_change.dart';
import 'package:panchang_at_this_moment/components/upcoming_muhurtas.dart' as upcoming;
import 'package:panchang_at_this_moment/components/sun_moon_combined_widget.dart';
import 'package:provider/provider.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:panchang_at_this_moment/stores/panchang_store.dart';

// Simplified component model
class PanchangComponent {
  final String id;
  final Widget widget;
  final ComponentType type;
  final int priority;

  PanchangComponent({
    required this.id,
    required this.widget,
    required this.type,
    this.priority = 0,
  });
}

enum ComponentType {
  muhurta,      // Time-sensitive muhurta information (top priority)
  sunMoon,      // Sun and moon times (always full width)
  tithiPaksha,  // Current tithi and paksha (full width)
  gridItem,     // Regular grid items
  upcomingMuhurtas, // Upcoming muhurtas (bottom section)
}

class PanchangAtTheMoment extends StatefulWidget {
  final DateTime startTime;
  final double lat;
  final double lng;
  final double alt;
  final bool isPlaying;
  final Function(DateTime)? onTimeChanged;
  final Function(bool)? onPlayStateChanged;
  const PanchangAtTheMoment({
    super.key,
    required this.startTime,
    required this.lat,
    required this.lng,
    required this.alt,
    required this.isPlaying,
    this.onTimeChanged,
    this.onPlayStateChanged,
  });

  @override
  State<PanchangAtTheMoment> createState() => _PanchangAtTheMomentState();
}

class _PanchangAtTheMomentState extends State<PanchangAtTheMoment> {
  late DateTime _currentTime = widget.startTime;
  late Timer _timer;

  void updateFromParent(DateTime newTime, bool newPlayState) {
    setState(() {
      _currentTime = newTime;
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(PanchangAtTheMoment oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.startTime != oldWidget.startTime) {
      setState(() {
        _currentTime = widget.startTime;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (widget.isPlaying) {
        setState(() {
          _currentTime = _currentTime.add(const Duration(seconds: 1));
        });
        widget.onTimeChanged?.call(_currentTime);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        final store = Provider.of<PanchangStore>(context);
        return _buildPanchangView(context, _currentTime, store);
      },
    );
  }

  // Simplified method to build all components
  List<PanchangComponent> _buildAllComponents(BuildContext context, DateTime currentTime, PanchangStore store) {
    if (store.translatedPanchangData == null) return [];

    final components = <PanchangComponent>[];
    final titleTextStyle = Theme.of(context).textTheme.titleLarge!;
    final smallTextStyle = Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 14);

    // 1. Muhurta component (if available)
    final muhurtaWidget = getPanchangItemSomeTimeChange(
      panchangData: store.translatedPanchangData!,
      currentTime: currentTime,
      titleTextStyle: titleTextStyle,
      smallTextStyle: smallTextStyle,
      context: context,
    );
    if (muhurtaWidget != null) {
      components.add(PanchangComponent(
        id: 'muhurta',
        widget: muhurtaWidget,
        type: ComponentType.muhurta,
        priority: 1,
      ));
    }


    components.add(PanchangComponent(
      id: 'sunMoon',
      widget: getSunMoonCombinedComponent(
        store.translatedPanchangData,
        DateTime(currentTime.year, currentTime.month, currentTime.day),
        context,
      ),
      type: ComponentType.sunMoon,
      priority: 2,
    ));


    // 3. Dynamic components
    final dynamicItems = getPanchangItemAlwayChange(
      panchangData: store.translatedPanchangData,
      context: context,
      currentTime: currentTime,
    );

    if (dynamicItems.isNotEmpty) {
      // Tithi Paksha component (first dynamic item) - full width
      components.add(PanchangComponent(
        id: 'tithiPaksha',
        widget: dynamicItems[0],
        type: ComponentType.tithiPaksha,
        priority: 2,
      ));

      // Rest as grid items
      for (int i = 1; i < dynamicItems.length; i++) {
        components.add(PanchangComponent(
          id: 'dynamic_$i',
          widget: dynamicItems[i],
          type: ComponentType.gridItem,
          priority: 4,
        ));
      }
    }


    // 4. Upcoming muhurtas component (at the bottom)
    components.add(PanchangComponent(
      id: 'upcomingMuhurtas',
      widget: upcoming.buildUpcomingMuhurtasWidget(
        panchangData: store.translatedPanchangData!,
        currentTime: currentTime,
        context: context,
      ),
      type: ComponentType.upcomingMuhurtas,
      priority: 6,
    ));

    // Sort by priority
    components.sort((a, b) => a.priority.compareTo(b.priority));

    
    return components;
  }

  // Simplified view builder
  Widget _buildPanchangView(BuildContext context, DateTime currentTime, PanchangStore store) {
    // Show loading state
    if (store.isLoading || !store.hasValidData) {
      return _buildLoadingView();
    }

    // Show error state
    if (store.errorMessage != null) {
      return _buildErrorView(store.errorMessage!);
    }

    // Build all components
    final components = _buildAllComponents(context, currentTime, store);

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF3E5AB), Colors.white],
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _buildComponentWidgets(components),
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF3E5AB), Colors.white],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 50,
              height: 50,
              child: CircularProgressIndicator(
                color: Color(0xFF6B4E3D),
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              "Loading Panchang data...",
              style: GoogleFonts.mallanna(
                color: const Color(0xFF5D4037),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildComponentWidgets(List<PanchangComponent> components) {
    final widgets = <Widget>[];
    final gridItems = <Widget>[];

    for (final component in components) {
      switch (component.type) {
        case ComponentType.muhurta:
          widgets.add(_buildMuhurtaContainer(component.widget));
          break;
        case ComponentType.sunMoon:
          widgets.add(_buildFullWidthContainer(component.widget));
          break;
        case ComponentType.tithiPaksha:
          widgets.add(_buildTithiPakshaContainer(component.widget));
          break;
        case ComponentType.gridItem:
          gridItems.add(component.widget);
          break;
        case ComponentType.upcomingMuhurtas:
          break;
      }
    }

    // Add grid items if any
    if (gridItems.isNotEmpty) {
      widgets.add(_buildGridContainer(gridItems));
    }

    for (final component in components) {
      switch (component.type) {
        case ComponentType.muhurta || ComponentType.tithiPaksha || ComponentType.sunMoon || ComponentType.gridItem:
          break;
        case ComponentType.upcomingMuhurtas:
          widgets.add(component.widget);
          break;
      }
    }

    return widgets;
  }

  Widget _buildMuhurtaContainer(Widget child) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.white, Color(0xFFFFFBF0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
      ),
      child: child,
    );
  }

  Widget _buildFullWidthContainer(Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      child: child,
    );
  }

  Widget _buildTithiPakshaContainer(Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(left: 4, right: 4, bottom: 8),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.white, Color(0xFFFFFDF7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
      ),
      child: child,
    );
  }

  Widget _buildGridContainer(List<Widget> gridItems) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Mobile-optimized layout: 2 columns in portrait, 3 in landscape
          final orientation = MediaQuery.of(context).orientation;
          final crossAxisCount = orientation == Orientation.landscape ? 3 : 2;

          // Calculate item size with mobile-optimized spacing
          double itemWidth = (constraints.maxWidth / crossAxisCount) - 4;

          return Wrap(
            spacing: 6.0,
            runSpacing: 6.0,
            alignment: WrapAlignment.center,
            runAlignment: WrapAlignment.center,
            children: gridItems.map((item) {
              return Container(
                width: itemWidth,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.white, Color(0xFFFFFDF7)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 3),
                    ),
                  ],
                  border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
                ),
                child: item,
              );
            }).toList(),
          );
        },
      ),
    );
  }

  Widget _buildErrorView(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error loading Panchang data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              final store = Provider.of<PanchangStore>(context, listen: false);
              store.refreshData();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}