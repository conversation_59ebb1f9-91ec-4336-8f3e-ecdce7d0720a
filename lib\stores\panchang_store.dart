import 'package:mobx/mobx.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

part 'panchang_store.g.dart';

class PanchangStore = _PanchangStore with _$PanchangStore;

abstract class _PanchangStore with Store {
  // Observable properties
  @observable
  PanchangDataForThreeDays? panchangData;

  @observable
  PanchangDataForThreeDays? translatedPanchangData;

  @observable
  DateTime currentDate = DateTime.now();

  @observable
  double? latitude;

  @observable
  double? longitude;

  @observable
  double altitude = 0.494;

  @observable
  String timezone = 'Asia/Kolkata';

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @observable
  String currentLanguage = 'en';

  // Cache key to track when API call is needed
  String? _lastApiCallKey;

  // Computed values
  @computed
  bool get hasLocationData => latitude != null && longitude != null;

  @computed
  bool get hasValidData => panchangData != null && !isLoading;

  @computed
  String get _currentApiCallKey {
    final dateKey = DateTime(currentDate.year, currentDate.month, currentDate.day).millisecondsSinceEpoch;
    return '$dateKey-$latitude-$longitude-$altitude-$timezone';
  }

  @computed
  bool get _shouldFetchData => _lastApiCallKey != _currentApiCallKey;

  // Actions
  @action
  Future<void> initializeStore() async {
    await loadLocationFromPreferences();
    if (hasLocationData) {
      await fetchPanchangData();
    }
  }

  @action
  Future<void> loadLocationFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      latitude = prefs.getDouble('lat') ?? 22.7196; // Default to Indore, India
      longitude = prefs.getDouble('lng') ?? 75.8577; // Default to Indore, India
    } catch (e) {
      errorMessage = 'Failed to load location preferences: $e';
    }
  }

  @action
  Future<void> saveLocationToPreferences() async {
    if (latitude != null && longitude != null) {
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setDouble('lat', latitude!);
        await prefs.setDouble('lng', longitude!);
      } catch (e) {
        errorMessage = 'Failed to save location preferences: $e';
      }
    }
  }

  @action
  Future<void> updateLocation(double lat, double lng) async {
    // Only update and fetch if location actually changed
    if (latitude != lat || longitude != lng) {
      debugPrint('PanchangStore: Location changed from ($latitude, $longitude) to ($lat, $lng)');
      latitude = lat;
      longitude = lng;
      await saveLocationToPreferences();
      await fetchPanchangData(); // Automatically fetch new data when location changes
    } else {
      debugPrint('PanchangStore: Location unchanged, skipping API call');
    }
  }

  @action
  Future<void> updateDate(DateTime newDate) async {
    // Only update and fetch if date actually changed (compare date only, not time)
    final currentDateOnly = DateTime(currentDate.year, currentDate.month, currentDate.day);
    final newDateOnly = DateTime(newDate.year, newDate.month, newDate.day);

    if (currentDateOnly != newDateOnly) {
      debugPrint('PanchangStore: Date changed from $currentDateOnly to $newDateOnly');
      currentDate = newDate;
      await fetchPanchangData(); // Automatically fetch new data when date changes
    } else {
      debugPrint('PanchangStore: Date unchanged, skipping API call');
    }
  }

  @action
  void updateLanguage(String languageCode) {
    if (currentLanguage != languageCode) {
      debugPrint('PanchangStore: Language changed from $currentLanguage to $languageCode');
      currentLanguage = languageCode;
      if (panchangData != null) {
        _translatePanchangData();
      }
    } else {
      debugPrint('PanchangStore: Language unchanged, skipping translation');
    }
  }

  @action
  Future<void> updateTimezone(String newTimezone) async {
    if (timezone != newTimezone) {
      debugPrint('PanchangStore: Timezone changed from $timezone to $newTimezone');
      timezone = newTimezone;
      await fetchPanchangData(); // Fetch new data when timezone changes
    } else {
      debugPrint('PanchangStore: Timezone unchanged, skipping API call');
    }
  }

  @action
  Future<void> updateAltitude(double newAltitude) async {
    if (altitude != newAltitude) {
      debugPrint('PanchangStore: Altitude changed from $altitude to $newAltitude');
      altitude = newAltitude;
      await fetchPanchangData(); // Fetch new data when altitude changes
    } else {
      debugPrint('PanchangStore: Altitude unchanged, skipping API call');
    }
  }

  @action
  Future<void> fetchPanchangData() async {
    if (!hasLocationData) {
      errorMessage = 'Location data not available';
      debugPrint('PanchangStore: Location data not available - lat: $latitude, lng: $longitude');
      return;
    }

    // Check if we need to fetch data based on current parameters
    final currentKey = _currentApiCallKey;
    if (_lastApiCallKey == currentKey) {
      debugPrint('PanchangStore: Data already fetched for current parameters, skipping API call');
      return;
    }

    debugPrint('PanchangStore: Starting to fetch panchang data for date: $currentDate, location: ($latitude, $longitude)');
    debugPrint('PanchangStore: API call key: $currentKey');
    isLoading = true;
    errorMessage = null;

    try {
      final DateTime previousDay = currentDate.subtract(const Duration(days: 1));
      final DateTime nextDay = currentDate.add(const Duration(days: 1));

      debugPrint('PanchangStore: Fetching data for dates: $previousDay, $currentDate, $nextDay');

      final List<Future<PanchangData>> futures = [
        PanchangAPI.getPanchangData(previousDay, latitude!, longitude!, altitude),
        PanchangAPI.getPanchangData(currentDate, latitude!, longitude!, altitude),
        PanchangAPI.getPanchangData(nextDay, latitude!, longitude!, altitude),
      ];

      final List<PanchangData> data = await Future.wait(futures);

      debugPrint('PanchangStore: Successfully fetched panchang data for all three days');

      panchangData = PanchangDataForThreeDays(
        previousDay: data[0],
        currentDay: data[1],
        nextDay: data[2],
      );

      _translatePanchangData();

      // Update cache key to indicate successful fetch
      _lastApiCallKey = currentKey;
      debugPrint('PanchangStore: Data translation completed, cache key updated: $currentKey');
    } catch (e, stackTrace) {
      errorMessage = 'Failed to fetch panchang data: $e';
      panchangData = null;
      translatedPanchangData = null;
      debugPrint('PanchangStore: Error fetching panchang data: $e');
      debugPrint('PanchangStore: Stack trace: $stackTrace');
    } finally {
      isLoading = false;
    }
  }

  @action
  void _translatePanchangData() {
    if (panchangData != null) {
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: panchangData!.previousDay.translate(currentLanguage),
        currentDay: panchangData!.currentDay.translate(currentLanguage),
        nextDay: panchangData!.nextDay.translate(currentLanguage),
      );
    }
  }

  @action
  void clearError() {
    errorMessage = null;
  }

  @action
  Future<void> refreshData() async {
    // Force refresh by clearing cache key
    _lastApiCallKey = null;
    await fetchPanchangData();
  }

  @action
  Future<void> updateParameters({
    double? lat,
    double? lng,
    double? alt,
    String? tz,
    DateTime? date,
  }) async {
    bool hasChanges = false;

    // Check for changes without triggering individual updates
    if (lat != null && latitude != lat) {
      latitude = lat;
      hasChanges = true;
      debugPrint('PanchangStore: Batch update - latitude changed to $lat');
    }

    if (lng != null && longitude != lng) {
      longitude = lng;
      hasChanges = true;
      debugPrint('PanchangStore: Batch update - longitude changed to $lng');
    }

    if (alt != null && altitude != alt) {
      altitude = alt;
      hasChanges = true;
      debugPrint('PanchangStore: Batch update - altitude changed to $alt');
    }

    if (tz != null && timezone != tz) {
      timezone = tz;
      hasChanges = true;
      debugPrint('PanchangStore: Batch update - timezone changed to $tz');
    }

    if (date != null) {
      final currentDateOnly = DateTime(currentDate.year, currentDate.month, currentDate.day);
      final newDateOnly = DateTime(date.year, date.month, date.day);
      if (currentDateOnly != newDateOnly) {
        currentDate = date;
        hasChanges = true;
        debugPrint('PanchangStore: Batch update - date changed to $newDateOnly');
      }
    }

    // Only fetch data if something actually changed
    if (hasChanges) {
      if (lat != null || lng != null) {
        await saveLocationToPreferences();
      }
      await fetchPanchangData();
    } else {
      debugPrint('PanchangStore: Batch update - no changes detected, skipping API call');
    }
  }
}
