import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/home_screen.dart';
import 'package:panchang_at_this_moment/onboarding_screen.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:panchang_at_this_moment/l10n/l10n.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:panchang_at_this_moment/stores/panchang_store.dart';

import 'package:panchang_at_this_moment/notifications/notification_service.dart';
import 'package:panchang_at_this_moment/notifications/notification_background_handler.dart';
import 'package:panchang_at_this_moment/notifications/notification_scheduler.dart'; // Import NotificationScheduler
import 'package:flutter/foundation.dart'; // Import for debugPrint

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await NotificationService.instance.init(notificationBackgroundHandler);
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
  });

  @override
  State<MyApp> createState() => MyAppState();

  static MyAppState? of(BuildContext context) =>
      context.findAncestorStateOfType<MyAppState>();
}

class MyAppState extends State<MyApp> {
  Locale _locale = const Locale('en');
  final _prefs = SharedPreferences.getInstance();
  bool _isFirstLaunch = true;
  bool _isLoading = true;
  late PanchangStore _panchangStore; // Declare PanchangStore

  @override
  void initState() {
    super.initState();
    _panchangStore = PanchangStore(); // Initialize PanchangStore
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    debugPrint('MyAppState: _initializeApp started.'); // Added log
    final prefs = await _prefs;

    // Check if onboarding has been completed
    final onboardingCompleted = prefs.getBool('onboarding_completed') ?? false;

    // Load saved locale
    final localeCode = prefs.getString('locale');
    if (localeCode != null) {
      _locale = Locale(localeCode);
    }

    // Initialize PanchangStore and fetch data
    debugPrint('MyAppState: Initializing PanchangStore...'); // Added log
    await _panchangStore.initializeStore();
    debugPrint('MyAppState: PanchangStore initialized. hasValidData: ${_panchangStore.hasValidData}, translatedPanchangData: ${_panchangStore.translatedPanchangData != null}'); // Added log

    // Schedule notifications after store is initialized and data is fetched
    if (_panchangStore.hasValidData && _panchangStore.translatedPanchangData != null) {
      debugPrint('MyAppState: Condition met for scheduling notifications.'); // Added log
      await NotificationService.instance.cancelAll(); // Clear any old notifications
      await NotificationScheduler(NotificationService.instance.plugin)
          .scheduleFor(_panchangStore.translatedPanchangData!);
      debugPrint('NotificationScheduler: Scheduled notifications on app start.');
    } else {
      debugPrint('MyAppState: Condition NOT met for scheduling notifications. Skipping.'); // Added log
    }


    setState(() {
      _isFirstLaunch = !onboardingCompleted;
      _isLoading = false;
    });
    debugPrint('MyAppState: _initializeApp finished. _isLoading: $_isLoading'); // Added log
  }

  void setLocale(Locale value) async {
    final prefs = await _prefs;
    prefs.setString('locale', value.languageCode);

    setState(() {
      _locale = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show loading screen while initializing
    if (_isLoading) {
      return MaterialApp(
        title: 'Panchang now',
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFF3E5AB), // Warm cream color
                  Colors.white,
                ],
              ),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF6B4E3D),
              ),
            ),
          ),
        ),
      );
    }

    return Provider<PanchangStore>.value( // Use .value constructor
      value: _panchangStore, // Provide the initialized store
      child: MaterialApp(
          title: 'Panchang now',
          debugShowCheckedModeBanner: false,
          supportedLocales: L10n.all,
          locale: _locale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate
          ],
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF6B4E3D), // Warm brown color for spiritual theme
            brightness: Brightness.light,
          ),
          textTheme: TextTheme(
            // Primary values - largest and boldest (for main time, dates, etc.)
            displayLarge: GoogleFonts.mallanna(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF3E2723),
              height: 1.2,
            ),
            displayMedium: GoogleFonts.mallanna(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF3E2723),
              height: 1.2,
            ),

            // Secondary values - medium size for important data
            headlineLarge: GoogleFonts.mallanna(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF3E2723),
              height: 1.3,
            ),
            headlineMedium: GoogleFonts.mallanna(
              fontSize: 22,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF3E2723),
              height: 1.3,
            ),

            // Labels - medium size, lighter weight
            titleLarge: GoogleFonts.peddana(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF6B4E3D),
              height: 1.4,
            ),
            titleMedium: GoogleFonts.peddana(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
              height: 1.4,
            ),

            // Secondary info - smallest size
            bodyLarge: GoogleFonts.mallanna(
              fontSize: 18,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF5D4037),
              height: 1.4,
            ),
            bodyMedium: GoogleFonts.mallanna(
              fontSize: 17,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF5D4037),
              height: 1.4,
            ),
            bodySmall: GoogleFonts.mallanna(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF8D6E63),
              height: 1.4,
            ),

            // Specialized labels
            labelLarge: GoogleFonts.peddana(
              fontSize: 17,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
              height: 1.4,
            ),
            labelMedium: GoogleFonts.peddana(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
              height: 1.4,
            ),
            labelSmall: GoogleFonts.peddana(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: const Color(0xFFBCAAA4),
              height: 1.4,
            ),
          ),
          cardTheme: CardTheme(
            elevation: 3,
            shadowColor: Colors.black.withValues(alpha: 0.08),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            color: Colors.white,
            margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
          ),
          appBarTheme: AppBarTheme(
            backgroundColor: const Color(0xFF6B4E3D),
            foregroundColor: Colors.white,
            titleTextStyle: GoogleFonts.peddana(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
        home: _isFirstLaunch ? const OnboardingScreen() : const HomeScreen()),
    );
  }
}