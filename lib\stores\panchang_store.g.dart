// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'panchang_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$PanchangStore on _PanchangStore, Store {
  Computed<bool>? _$hasLocationDataComputed;

  @override
  bool get hasLocationData =>
      (_$hasLocationDataComputed ??= Computed<bool>(() => super.hasLocationData,
              name: '_PanchangStore.hasLocationData'))
          .value;
  Computed<bool>? _$hasValidDataComputed;

  @override
  bool get hasValidData =>
      (_$hasValidDataComputed ??= Computed<bool>(() => super.hasValidData,
              name: '_PanchangStore.hasValidData'))
          .value;
  Computed<String>? _$_currentApiCallKeyComputed;

  @override
  String get _currentApiCallKey => (_$_currentApiCallKeyComputed ??=
          Computed<String>(() => super._currentApiCallKey,
              name: '_PanchangStore._currentApiCallKey'))
      .value;
  Computed<bool>? _$_shouldFetchDataComputed;

  @override
  bool get _shouldFetchData => (_$_shouldFetchDataComputed ??= Computed<bool>(
          () => super._shouldFetchData,
          name: '_PanchangStore._shouldFetchData'))
      .value;

  late final _$panchangDataAtom =
      Atom(name: '_PanchangStore.panchangData', context: context);

  @override
  PanchangDataForThreeDays? get panchangData {
    _$panchangDataAtom.reportRead();
    return super.panchangData;
  }

  @override
  set panchangData(PanchangDataForThreeDays? value) {
    _$panchangDataAtom.reportWrite(value, super.panchangData, () {
      super.panchangData = value;
    });
  }

  late final _$translatedPanchangDataAtom =
      Atom(name: '_PanchangStore.translatedPanchangData', context: context);

  @override
  PanchangDataForThreeDays? get translatedPanchangData {
    _$translatedPanchangDataAtom.reportRead();
    return super.translatedPanchangData;
  }

  @override
  set translatedPanchangData(PanchangDataForThreeDays? value) {
    _$translatedPanchangDataAtom
        .reportWrite(value, super.translatedPanchangData, () {
      super.translatedPanchangData = value;
    });
  }

  late final _$currentDateAtom =
      Atom(name: '_PanchangStore.currentDate', context: context);

  @override
  DateTime get currentDate {
    _$currentDateAtom.reportRead();
    return super.currentDate;
  }

  @override
  set currentDate(DateTime value) {
    _$currentDateAtom.reportWrite(value, super.currentDate, () {
      super.currentDate = value;
    });
  }

  late final _$latitudeAtom =
      Atom(name: '_PanchangStore.latitude', context: context);

  @override
  double? get latitude {
    _$latitudeAtom.reportRead();
    return super.latitude;
  }

  @override
  set latitude(double? value) {
    _$latitudeAtom.reportWrite(value, super.latitude, () {
      super.latitude = value;
    });
  }

  late final _$longitudeAtom =
      Atom(name: '_PanchangStore.longitude', context: context);

  @override
  double? get longitude {
    _$longitudeAtom.reportRead();
    return super.longitude;
  }

  @override
  set longitude(double? value) {
    _$longitudeAtom.reportWrite(value, super.longitude, () {
      super.longitude = value;
    });
  }

  late final _$altitudeAtom =
      Atom(name: '_PanchangStore.altitude', context: context);

  @override
  double get altitude {
    _$altitudeAtom.reportRead();
    return super.altitude;
  }

  @override
  set altitude(double value) {
    _$altitudeAtom.reportWrite(value, super.altitude, () {
      super.altitude = value;
    });
  }

  late final _$timezoneAtom =
      Atom(name: '_PanchangStore.timezone', context: context);

  @override
  String get timezone {
    _$timezoneAtom.reportRead();
    return super.timezone;
  }

  @override
  set timezone(String value) {
    _$timezoneAtom.reportWrite(value, super.timezone, () {
      super.timezone = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_PanchangStore.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$errorMessageAtom =
      Atom(name: '_PanchangStore.errorMessage', context: context);

  @override
  String? get errorMessage {
    _$errorMessageAtom.reportRead();
    return super.errorMessage;
  }

  @override
  set errorMessage(String? value) {
    _$errorMessageAtom.reportWrite(value, super.errorMessage, () {
      super.errorMessage = value;
    });
  }

  late final _$currentLanguageAtom =
      Atom(name: '_PanchangStore.currentLanguage', context: context);

  @override
  String get currentLanguage {
    _$currentLanguageAtom.reportRead();
    return super.currentLanguage;
  }

  @override
  set currentLanguage(String value) {
    _$currentLanguageAtom.reportWrite(value, super.currentLanguage, () {
      super.currentLanguage = value;
    });
  }

  late final _$initializeStoreAsyncAction =
      AsyncAction('_PanchangStore.initializeStore', context: context);

  @override
  Future<void> initializeStore() {
    return _$initializeStoreAsyncAction.run(() => super.initializeStore());
  }

  late final _$loadLocationFromPreferencesAsyncAction = AsyncAction(
      '_PanchangStore.loadLocationFromPreferences',
      context: context);

  @override
  Future<void> loadLocationFromPreferences() {
    return _$loadLocationFromPreferencesAsyncAction
        .run(() => super.loadLocationFromPreferences());
  }

  late final _$saveLocationToPreferencesAsyncAction =
      AsyncAction('_PanchangStore.saveLocationToPreferences', context: context);

  @override
  Future<void> saveLocationToPreferences() {
    return _$saveLocationToPreferencesAsyncAction
        .run(() => super.saveLocationToPreferences());
  }

  late final _$updateLocationAsyncAction =
      AsyncAction('_PanchangStore.updateLocation', context: context);

  @override
  Future<void> updateLocation(double lat, double lng) {
    return _$updateLocationAsyncAction
        .run(() => super.updateLocation(lat, lng));
  }

  late final _$updateDateAsyncAction =
      AsyncAction('_PanchangStore.updateDate', context: context);

  @override
  Future<void> updateDate(DateTime newDate) {
    return _$updateDateAsyncAction.run(() => super.updateDate(newDate));
  }

  late final _$updateTimezoneAsyncAction =
      AsyncAction('_PanchangStore.updateTimezone', context: context);

  @override
  Future<void> updateTimezone(String newTimezone) {
    return _$updateTimezoneAsyncAction
        .run(() => super.updateTimezone(newTimezone));
  }

  late final _$updateAltitudeAsyncAction =
      AsyncAction('_PanchangStore.updateAltitude', context: context);

  @override
  Future<void> updateAltitude(double newAltitude) {
    return _$updateAltitudeAsyncAction
        .run(() => super.updateAltitude(newAltitude));
  }

  late final _$fetchPanchangDataAsyncAction =
      AsyncAction('_PanchangStore.fetchPanchangData', context: context);

  @override
  Future<void> fetchPanchangData() {
    return _$fetchPanchangDataAsyncAction.run(() => super.fetchPanchangData());
  }

  late final _$refreshDataAsyncAction =
      AsyncAction('_PanchangStore.refreshData', context: context);

  @override
  Future<void> refreshData() {
    return _$refreshDataAsyncAction.run(() => super.refreshData());
  }

  late final _$updateParametersAsyncAction =
      AsyncAction('_PanchangStore.updateParameters', context: context);

  @override
  Future<void> updateParameters(
      {double? lat, double? lng, double? alt, String? tz, DateTime? date}) {
    return _$updateParametersAsyncAction.run(() => super
        .updateParameters(lat: lat, lng: lng, alt: alt, tz: tz, date: date));
  }

  late final _$_PanchangStoreActionController =
      ActionController(name: '_PanchangStore', context: context);

  @override
  void updateLanguage(String languageCode) {
    final _$actionInfo = _$_PanchangStoreActionController.startAction(
        name: '_PanchangStore.updateLanguage');
    try {
      return super.updateLanguage(languageCode);
    } finally {
      _$_PanchangStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _translatePanchangData() {
    final _$actionInfo = _$_PanchangStoreActionController.startAction(
        name: '_PanchangStore._translatePanchangData');
    try {
      return super._translatePanchangData();
    } finally {
      _$_PanchangStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearError() {
    final _$actionInfo = _$_PanchangStoreActionController.startAction(
        name: '_PanchangStore.clearError');
    try {
      return super.clearError();
    } finally {
      _$_PanchangStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
panchangData: ${panchangData},
translatedPanchangData: ${translatedPanchangData},
currentDate: ${currentDate},
latitude: ${latitude},
longitude: ${longitude},
altitude: ${altitude},
timezone: ${timezone},
isLoading: ${isLoading},
errorMessage: ${errorMessage},
currentLanguage: ${currentLanguage},
hasLocationData: ${hasLocationData},
hasValidData: ${hasValidData}
    ''';
  }
}
