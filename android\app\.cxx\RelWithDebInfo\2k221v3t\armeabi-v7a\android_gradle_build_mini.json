{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutterProjects\\panchang_at_this_moment\\android\\app\\.cxx\\RelWithDebInfo\\2k221v3t\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutterProjects\\panchang_at_this_moment\\android\\app\\.cxx\\RelWithDebInfo\\2k221v3t\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}