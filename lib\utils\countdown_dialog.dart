import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CountDownItem {
  final DateTime startTime;
  final DateTime endTime;
  final String title;
  final String? category; // Optional category/key to show alongside the title

  CountDownItem({
    required this.endTime,
    required this.title,
    required this.startTime,
    this.category,
  });
}

class CountDownWithTimeDialog extends StatefulWidget {
  final Widget child;
  final DateTime currentTime;
  final List<CountDownItem> countDownItems;

  const CountDownWithTimeDialog(
      {super.key,
      required this.child,
      required this.countDownItems,
      required this.currentTime});

  @override
  State<CountDownWithTimeDialog> createState() =>
      _CountDownWithTimeDialogState();
}

class _CountDownWithTimeDialogState extends State<CountDownWithTimeDialog> {
  String _formatDuration(Duration duration) {
    int totalSeconds = duration.inSeconds;
    int totalMinutes = duration.inMinutes;
    int totalHours = duration.inHours;
    int days = duration.inDays;
    int hours = totalHours % 24;
    int minutes = totalMinutes % 60;
    int seconds = totalSeconds % 60;

    if (totalMinutes < 60) {
      // Less than 1 hour: show only minutes and seconds
      return '${minutes.toString().padLeft(2, '0')} mins ${seconds.toString().padLeft(2, '0')} secs';
    } else if (totalHours >= 24) {
      // More than or equal to 24 hours: show days, hours, minutes
      return '${days.toString()} days ${hours.toString().padLeft(2, '0')} hrs ${minutes.toString().padLeft(2, '0')} mins';
    } else {
      // Between 1 hour and 24 hours: show hours, minutes, seconds
      return '${totalHours.toString().padLeft(2, '0')} hrs ${minutes.toString().padLeft(2, '0')} mins ${seconds.toString().padLeft(2, '0')} secs';
    }
  }

  String _formatEndTime(DateTime endTime) {
    final now = widget.currentTime;
    final isSameDay = DateUtils.isSameDay(endTime, now);
    final timeFormat = DateFormat.jm();

    if (isSameDay) {
      return timeFormat.format(endTime);
    }

    // Check if it's yesterday or tomorrow
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final tomorrow = DateTime(now.year, now.month, now.day + 1);

    if (DateUtils.isSameDay(endTime, yesterday)) {
      return '${AppLocalizations.of(context)!.yesterday} ${timeFormat.format(endTime)}';
    } else if (DateUtils.isSameDay(endTime, tomorrow)) {
      return '${AppLocalizations.of(context)!.tomorrow} ${timeFormat.format(endTime)}';
    }

    // For other dates, show the full date
    return DateFormat('dd-MM-yyyy').add_jm().format(endTime);
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String label,
      String value, Color iconColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 14,
          ),
        ),
        const SizedBox(width: 10),
        Text(
          '$label : ',
          style: Theme.of(context).textTheme.labelMedium!.copyWith(
                color: const Color(0xFF8D6E63),
                fontSize: 18,
              ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: const Color(0xFF5D4037),
                  fontWeight: FontWeight.w500,
                  fontSize: 15,
                ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              backgroundColor: Colors.white,
              elevation: 4,
              shadowColor: Colors.black.withValues(alpha: 0.15),
              titlePadding: EdgeInsets.zero,
              contentPadding: const EdgeInsets.fromLTRB(10, 10, 10, 20),
              title: Container(
                padding: const EdgeInsets.fromLTRB(20, 20, 16, 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.1),
                      Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .primary
                                .withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            Icons.timer,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Panchang Timer',
                          style:
                              Theme.of(context).textTheme.titleLarge!.copyWith(
                                    color: const Color(0xFF3E2723),
                                    fontSize: 22,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ],
                    ),
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(20),
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.close,
                            color: Color(0xFF8D6E63),
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    for (CountDownItem item in widget.countDownItems)
                      StreamBuilder(
                        stream: Stream.periodic(
                            const Duration(seconds: 1), (i) => i),
                        builder: (context, snapshot) {
                          return item.endTime.isAfter(widget.currentTime)
                              ? Container(
                                  width: double.infinity,
                                  margin: const EdgeInsets.only(bottom: 16),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [Colors.white, Color(0xFFFFFDF7)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(4),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black
                                            .withValues(alpha: 0.08),
                                        blurRadius: 8,
                                        spreadRadius: 0,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: const Color(0xFFE8E8E8),
                                      width: 0.5,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0, vertical: 8.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Stack(
                                          children: [
                                            Padding(
                                              padding: (item.category != null)
                                                  ? const EdgeInsets.only(
                                                      top: 18.0)
                                                  : const EdgeInsets.all(0),
                                              child: Row(
                                                children: [
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(4),
                                                    decoration: BoxDecoration(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .primary
                                                          .withValues(
                                                              alpha: 0.1),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    child: Icon(
                                                      Icons.schedule,
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .primary,
                                                      size: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  Expanded(
                                                    child: Text(
                                                      item.title,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .headlineMedium!
                                                          .copyWith(
                                                            fontSize: 18,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color: const Color(
                                                                0xFF3E2723),
                                                          ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            if (item.category != null)
                                              Positioned(
                                                top: 0,
                                                right: 0,
                                                child: Text(
                                                  item.category!,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelMedium!
                                                      .copyWith(
                                                        fontSize: 18,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: const Color(
                                                            0xFF8D6E63),
                                                      ),
                                                ),
                                              ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        _buildInfoRow(
                                          context,
                                          Icons.play_arrow,
                                          AppLocalizations.of(context)!.start,
                                          _formatEndTime(item.startTime),
                                          const Color(0xFF4CAF50),
                                        ),
                                        const SizedBox(height: 12),
                                        _buildInfoRow(
                                          context,
                                          Icons.stop,
                                          AppLocalizations.of(context)!.end,
                                          _formatEndTime(item.endTime),
                                          const Color(0xFFFF9800),
                                        ),
                                        const SizedBox(height: 12),
                                        Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: [
                                                Theme.of(context)
                                                    .colorScheme
                                                    .primary
                                                    .withValues(alpha: 0.05),
                                                Theme.of(context)
                                                    .colorScheme
                                                    .primary
                                                    .withValues(alpha: 0.02),
                                              ],
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            border: Border.all(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .primary
                                                  .withValues(alpha: 0.2),
                                              width: 1,
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Column(
                                                children: [
                                                  Icon(
                                                    Icons.hourglass_bottom,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary,
                                                    size: 18,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    AppLocalizations.of(
                                                            context)!
                                                        .timeLeft,
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .labelLarge!
                                                        .copyWith(
                                                          color: const Color(
                                                              0xFF6B4E3D),
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Text(
                                                  _formatDuration(item.endTime
                                                      .difference(
                                                          widget.currentTime)),
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodyLarge!
                                                      .copyWith(
                                                        color: const Color(
                                                            0xFF3E2723),
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 14,
                                                      ),
                                                  textAlign: TextAlign.end,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink();
                        },
                      ),
                  ],
                ),
              ),
            );
          },
        );
      },
      child: Container(color: Colors.transparent, child: widget.child),
    );
  }
}
