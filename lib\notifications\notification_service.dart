import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class NotificationService {
  NotificationService._();
  static final NotificationService instance = NotificationService._();

  final FlutterLocalNotificationsPlugin plugin = FlutterLocalNotificationsPlugin();
  bool _initialized = false;

  Future<void> init(DidReceiveBackgroundNotificationResponseCallback onDidReceiveBackgroundNotificationResponse) async {
    if (_initialized) return;

    // Initialize time zones and set to Asia/Kolkata to match API times
    tz.initializeTimeZones();
    tz.setLocalLocation(tz.getLocation('Asia/Kolkata'));

    const androidInit = AndroidInitializationSettings('@mipmap/ic_launcher');

    await plugin.initialize(
      const InitializationSettings(android: androidInit),
      onDidReceiveNotificationResponse: (resp) {
        if (kDebugMode) {
          debugPrint('Notification tapped: ${resp.payload}');
        }
      },
      onDidReceiveBackgroundNotificationResponse: onDidReceiveBackgroundNotificationResponse,
    );

    final android = plugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    // Request permission on Android 13+
    await android?.requestNotificationsPermission();
    await android?.requestExactAlarmsPermission();

    // Create a channel for muhurtas
    const channel = AndroidNotificationChannel(
      'muhurta_channel',
      'Muhurtas',
      description: 'Muhurta alerts',
      importance: Importance.max,
    );
    await android?.createNotificationChannel(channel);

    _initialized = true;
  }

  Future<void> cancelAll() async {
    await plugin.cancelAll();
  }

  tz.Location get tzLoc => tz.getLocation('Asia/Kolkata');
}

