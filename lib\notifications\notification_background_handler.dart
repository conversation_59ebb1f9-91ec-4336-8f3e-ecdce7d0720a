import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'dart:convert';

// This is a top-level function that will be called by the plugin when a notification is received
// while the app is in the background or terminated.
@pragma('vm:entry-point')
void notificationBackgroundHandler(NotificationResponse notificationResponse) async {
  // We need to re-initialize timezone data in the background isolate
  tz.initializeTimeZones();
  tz.setLocalLocation(tz.getLocation('Asia/Kolkata'));

  // Handle notification tap
  if (notificationResponse.payload != null) {
    final payload = jsonDecode(notificationResponse.payload!);
    debugPrint('Background notification tapped with payload: $payload');
    // You can add more complex logic here, e.g., navigate to a specific screen
    // when the app is opened from a background notification.
  }
}
