import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';

void main() {
  group('CountDownItem', () {
    test('should create CountDownItem with category', () {
      final startTime = DateTime.now();
      final endTime = startTime.add(const Duration(hours: 1));
      
      final item = CountDownItem(
        title: 'Mula',
        startTime: startTime,
        endTime: endTime,
        category: 'Nakshatra',
      );
      
      expect(item.title, equals('Mula'));
      expect(item.category, equals('Nakshatra'));
      expect(item.startTime, equals(startTime));
      expect(item.endTime, equals(endTime));
    });

    test('should create CountDownItem without category', () {
      final startTime = DateTime.now();
      final endTime = startTime.add(const Duration(hours: 1));
      
      final item = CountDownItem(
        title: 'Tith<PERSON> Paksha',
        startTime: startTime,
        endTime: endTime,
      );
      
      expect(item.title, equals('Tithi Paksha'));
      expect(item.category, isNull);
      expect(item.startTime, equals(startTime));
      expect(item.endTime, equals(endTime));
    });
  });

  group('CountDownWithTimeDialog', () {
    testWidgets('should display category and title when category is provided', (WidgetTester tester) async {
      final currentTime = DateTime.now();
      final startTime = currentTime.subtract(const Duration(minutes: 30));
      final endTime = currentTime.add(const Duration(hours: 1));

      final countDownItems = [
        CountDownItem(
          title: 'Mula',
          startTime: startTime,
          endTime: endTime,
          category: 'Nakshatra',
        ),
        CountDownItem(
          title: 'Dhanu',
          startTime: startTime,
          endTime: endTime,
          category: 'Rashi',
        ),
        CountDownItem(
          title: 'Shukla Panchami',
          startTime: startTime,
          endTime: endTime,
          category: 'Tithi Paksha',
        ),
        CountDownItem(
          title: 'Bava',
          startTime: startTime,
          endTime: endTime,
          category: 'Karana',
        ),
        CountDownItem(
          title: 'Shiva',
          startTime: startTime,
          endTime: endTime,
          category: 'Yoga',
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en', ''),
          ],
          home: Scaffold(
            body: CountDownWithTimeDialog(
              currentTime: currentTime,
              countDownItems: countDownItems,
              child: const Text('Test'),
            ),
          ),
        ),
      );

      // Tap to open the dialog
      await tester.tap(find.text('Test'));
      await tester.pumpAndSettle();

      // Verify that both category and title are displayed
      expect(find.text('Nakshatra'), findsOneWidget);
      expect(find.text('Mula'), findsOneWidget);
      expect(find.text('Rashi'), findsOneWidget);
      expect(find.text('Dhanu'), findsOneWidget);
      expect(find.text('Tithi Paksha'), findsOneWidget);
      expect(find.text('Shukla Panchami'), findsOneWidget);
      expect(find.text('Karana'), findsOneWidget);
      expect(find.text('Bava'), findsOneWidget);
      expect(find.text('Yoga'), findsOneWidget);
      expect(find.text('Shiva'), findsOneWidget);
    });

    testWidgets('should display only title when category is not provided', (WidgetTester tester) async {
      final currentTime = DateTime.now();
      final startTime = currentTime.subtract(const Duration(minutes: 30));
      final endTime = currentTime.add(const Duration(hours: 1));
      
      final countDownItems = [
        CountDownItem(
          title: 'Tithi Paksha',
          startTime: startTime,
          endTime: endTime,
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en', ''),
          ],
          home: Scaffold(
            body: CountDownWithTimeDialog(
              currentTime: currentTime,
              countDownItems: countDownItems,
              child: const Text('Test'),
            ),
          ),
        ),
      );

      // Tap to open the dialog
      await tester.tap(find.text('Test'));
      await tester.pumpAndSettle();

      // Verify that only title is displayed
      expect(find.text('Tithi Paksha'), findsOneWidget);
    });
  });
}
